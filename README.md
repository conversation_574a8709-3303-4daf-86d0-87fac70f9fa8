# ATMA Frontend - AI-Driven Talent Mapping Assessment

Frontend aplikasi untuk ATMA (AI-Driven Talent Mapping Assessment) yang menyediakan fitur register dan login sederhana.

## 🚀 Tech Stack

- **React 18** - UI Library
- **Vite** - Build tool dan dev server
- **Zustand** - State management
- **React Router DOM** - Routing
- **Tailwind CSS** - Styling
- **React Hook Form** - Form handling
- **Zod** - Validation
- **Axios** - HTTP client
- **Lucide React** - Icons

## 📋 Features

- ✅ User Registration dengan validasi
- ✅ User Login dengan JWT authentication
- ✅ Protected routes
- ✅ Dashboard sederhana
- ✅ Responsive design
- ✅ Error handling
- ✅ Loading states
- ✅ Form validation

## 🛠️ Installation

1. Clone repository atau pastikan Anda berada di folder project
2. Install dependencies:
```bash
npm install
```

3. Jalankan development server:
```bash
npm run dev
```

4. Buka browser dan akses: `http://localhost:5173`

## 📁 Project Structure

```
src/
├── components/
│   ├── ui/                 # Reusable UI components
│   │   ├── Button.jsx
│   │   ├── Input.jsx
│   │   ├── Card.jsx
│   │   └── Alert.jsx
│   ├── auth/               # Authentication components
│   │   ├── LoginForm.jsx
│   │   └── RegisterForm.jsx
│   └── ProtectedRoute.jsx  # Route protection
├── pages/
│   ├── Login.jsx
│   ├── Register.jsx
│   └── Dashboard.jsx
├── store/
│   └── authStore.js        # Zustand auth store
├── services/
│   └── api.js              # API service dengan axios
├── lib/
│   └── utils.js            # Utility functions
└── App.jsx                 # Main app dengan routing
```

## 🔧 API Integration

Aplikasi ini terintegrasi dengan ATMA API Gateway yang berjalan di `http://localhost:3000`.

### Endpoints yang digunakan:

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile

### Validation Rules:

**Email:**
- Valid email format
- Maximum 255 characters
- Required

**Password:**
- Minimum 8 characters
- Must contain at least one letter and one number
- Required

## 🎨 UI Components

Aplikasi menggunakan komponen UI custom yang dibuat dengan Tailwind CSS:

- **Button** - Dengan variants (default, destructive, outline, etc.)
- **Input** - Dengan error states
- **Card** - Layout container
- **Alert** - Error dan success messages

## 🔐 Authentication Flow

1. User mengakses aplikasi
2. Jika belum login, diarahkan ke halaman login
3. User bisa register akun baru atau login dengan akun existing
4. Setelah login berhasil, JWT token disimpan di localStorage
5. User diarahkan ke dashboard
6. Protected routes akan mengecek authentication status

## 🚦 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🌐 Environment

Aplikasi ini dikonfigurasi untuk development dengan:
- Frontend: `http://localhost:5173`
- Backend API: `http://localhost:3000`

## 📝 Usage

1. **Register**: Buat akun baru dengan email dan password
2. **Login**: Masuk dengan kredensial yang sudah dibuat
3. **Dashboard**: Lihat informasi user dan token balance
4. **Logout**: Keluar dari aplikasi

## 🔍 Error Handling

- Form validation dengan Zod
- API error handling dengan interceptors
- User-friendly error messages
- Loading states untuk UX yang baik

## 🎯 Next Steps

Fitur yang bisa ditambahkan selanjutnya:
- Profile management
- Assessment submission
- Results viewing
- Token management
- Real-time notifications dengan WebSocket
