/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M12.662 21H5a2 2 0 0 1-2-2v-9a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v2.475",
      key: "uubd2h"
    }
  ],
  ["path", { d: "M14.959 12.717A1 1 0 0 0 14 12h-4a1 1 0 0 0-1 1v8", key: "p7f341" }],
  ["path", { d: "M15 18h6", key: "3b3c90" }],
  ["path", { d: "M18 15v6", key: "9wciyi" }]
];
const HousePlus = createLucideIcon("house-plus", __iconNode);

export { __iconNode, HousePlus as default };
//# sourceMappingURL=house-plus.js.map
