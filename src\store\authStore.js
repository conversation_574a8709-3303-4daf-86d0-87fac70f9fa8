import { create } from 'zustand'
import { authAPI } from '../services/api'

const useAuthStore = create((set, get) => ({
  // State
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  // Actions
  setLoading: (loading) => set({ isLoading: loading }),
  
  setError: (error) => set({ error }),
  
  clearError: () => set({ error: null }),

  // Initialize auth state from localStorage
  initializeAuth: () => {
    const token = localStorage.getItem('token')
    const user = localStorage.getItem('user')
    
    if (token && user) {
      try {
        const parsedUser = JSON.parse(user)
        set({
          token,
          user: parsedUser,
          isAuthenticated: true,
        })
      } catch (error) {
        // Invalid stored data, clear it
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      }
    }
  },

  // Register function
  register: async (email, password) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await authAPI.register(email, password)
      
      if (response.success) {
        set({ 
          isLoading: false,
          error: null 
        })
        return { success: true, message: 'Registration successful! Please login.' }
      } else {
        throw new Error(response.error?.message || 'Registration failed')
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'Registration failed'
      set({ 
        isLoading: false, 
        error: errorMessage 
      })
      return { success: false, error: errorMessage }
    }
  },

  // Login function
  login: async (email, password) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await authAPI.login(email, password)
      
      if (response.success && response.data) {
        const { user, token } = response.data
        
        // Store in localStorage
        localStorage.setItem('token', token)
        localStorage.setItem('user', JSON.stringify(user))
        
        set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        })
        
        return { success: true }
      } else {
        throw new Error(response.error?.message || 'Login failed')
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'Login failed'
      set({ 
        isLoading: false, 
        error: errorMessage 
      })
      return { success: false, error: errorMessage }
    }
  },

  // Logout function
  logout: async () => {
    set({ isLoading: true })
    
    try {
      await authAPI.logout()
    } catch (error) {
      // Even if logout API fails, we still want to clear local state
      console.error('Logout API error:', error)
    } finally {
      // Clear localStorage
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      
      // Reset state
      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      })
    }
  },

  // Get user profile
  getProfile: async () => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await authAPI.getProfile()
      
      if (response.success && response.data) {
        const updatedUser = { ...get().user, ...response.data }
        localStorage.setItem('user', JSON.stringify(updatedUser))
        
        set({
          user: updatedUser,
          isLoading: false,
        })
        
        return { success: true, data: response.data }
      } else {
        throw new Error(response.error?.message || 'Failed to get profile')
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error?.message || error.message || 'Failed to get profile'
      set({ 
        isLoading: false, 
        error: errorMessage 
      })
      return { success: false, error: errorMessage }
    }
  },
}))

export default useAuthStore
