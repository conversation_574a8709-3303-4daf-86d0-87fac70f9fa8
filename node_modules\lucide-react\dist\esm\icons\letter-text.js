/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M15 12h6", key: "upa0zy" }],
  ["path", { d: "M15 6h6", key: "1jlkvy" }],
  ["path", { d: "m3 13 3.553-7.724a.5.5 0 0 1 .894 0L11 13", key: "blevx4" }],
  ["path", { d: "M3 18h18", key: "1h113x" }],
  ["path", { d: "M3.92 11h6.16", key: "1bqo8m" }]
];
const LetterText = createLucideIcon("letter-text", __iconNode);

export { __iconNode, LetterText as default };
//# sourceMappingURL=letter-text.js.map
