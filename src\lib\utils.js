import { clsx } from "clsx"

export function cn(...inputs) {
  return clsx(inputs)
}

export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 255
}

export function validatePassword(password) {
  // Minimum 8 characters, must contain at least one letter and one number
  const hasLetter = /[a-zA-Z]/.test(password)
  const hasNumber = /\d/.test(password)
  return password.length >= 8 && hasLetter && hasNumber
}
