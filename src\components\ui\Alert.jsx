import { cn } from "../../lib/utils"
import { AlertCircle, CheckCircle, Info, X } from "lucide-react"

const Alert = ({ className, variant = "default", children, onClose, ...props }) => {
  const variants = {
    default: "bg-gray-50 border-gray-200 text-gray-900",
    destructive: "bg-red-50 border-red-200 text-red-900",
    success: "bg-green-50 border-green-200 text-green-900",
    warning: "bg-yellow-50 border-yellow-200 text-yellow-900",
  }

  const icons = {
    default: Info,
    destructive: AlertCircle,
    success: CheckCircle,
    warning: AlertCircle,
  }

  const Icon = icons[variant]

  return (
    <div
      className={cn(
        "relative w-full rounded-lg border p-4",
        variants[variant],
        className
      )}
      {...props}
    >
      <div className="flex items-start">
        <Icon className="h-4 w-4 mt-0.5 mr-3 flex-shrink-0" />
        <div className="flex-1">{children}</div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-3 flex-shrink-0 opacity-70 hover:opacity-100"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  )
}

const AlertDescription = ({ className, ...props }) => (
  <div
    className={cn("text-sm [&_p]:leading-relaxed", className)}
    {...props}
  />
)

export { Alert, AlertDescription }
